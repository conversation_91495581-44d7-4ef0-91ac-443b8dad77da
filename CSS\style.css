/* ===== CSS VARIABLES ===== */
:root {
  --background: #000000;
  --surface-1: #111111;
  --surface-2: #1f1f1f;
  --surface-3: #2c2c2c;
  --text-primary: #ffffff;
  --text-secondary: #a0a0a0;
  --border-color: #3a3a3a;
  --accent-color: #ffffff;
  --accent-text: #000000;

  /* New design system variables */
  --panel-width-collapsed: 0px;
  --panel-width-expanded: 224px;
  --chat-width-collapsed: 0px;
  --chat-width-expanded: 320px;
  --input-panel-height-collapsed: 60px;
  --input-panel-height-expanded: 200px;
  --floating-controls-size: 40px;
  --border-radius: 12px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  background-color: var(--background);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

html {
  height: 100%;
  overflow: hidden;
}

/* ===== SCROLLBAR STYLES ===== */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--surface-1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background-color: var(--surface-3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

::-webkit-scrollbar-corner {
  background: var(--surface-1);
}

/* ===== ANIMATIONS ===== */
.loader {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #333;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* ===== FLOATING INPUT PANEL ===== */
#input-panel {
  animation: slideInFromTop 0.3s ease-out;
  /* backdrop-filter: blur(10px); */ /* Disabled - remove comment to re-enable */
}

#input-panel.collapsed #input-panel-content {
  display: none;
}

#input-panel.collapsed #panel-chevron {
  transform: rotate(180deg);
}

#panel-chevron {
  transition: transform 0.3s ease;
}

/* ===== SVG DISPLAY OPTIMIZATION ===== */
#output {
  position: absolute !important;
  background: var(--background);
  z-index: 10;
  pointer-events: auto;
}

#output.panning {
  cursor: grabbing !important;
}

#output svg {
  max-width: none !important;
  max-height: none !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  object-fit: contain !important;
  pointer-events: auto !important;
  z-index: 5;
}

#output #placeholder-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  font-size: 1.125rem;
  font-weight: 500;
}

#output #error-message {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  max-width: 80%;
  text-align: center;
}

/* SVG Node Interactions */
#output .node {
  cursor: pointer;
  transition: all 0.2s ease;
}

#output .node:hover rect,
#output .node:hover polygon,
#output .node:hover ellipse,
#output .node:hover circle {
  stroke-width: 2px !important;
  stroke: var(--accent-color) !important;
  filter: brightness(1.1);
}

/* ===== FLOATING CONTROLS ===== */
.floating-control {
  width: var(--floating-controls-size);
  height: var(--floating-controls-size);
  background: var(--surface-1);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  /* backdrop-filter: blur(10px); */ /* Disabled */
  z-index: 50;
  position: relative;
}

.floating-control:hover {
  background: var(--surface-2);
  transform: translateY(-1px);
  box-shadow: var(--shadow-xl);
}

.floating-control:active {
  transform: translateY(0);
}

/* Desktop Toggle Buttons */
#toggle-sidebar,
#toggle-chat {
  z-index: 50;
  position: relative;
  background: var(--surface-1);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
}

#toggle-sidebar:hover,
#toggle-chat:hover {
  background: var(--surface-2);
  transform: translateY(-1px);
}

/* ===== PANEL STYLES ===== */
#history-panel {
  width: var(--panel-width-expanded);
  transition: width 0.3s ease, transform 0.3s ease;
}

#history-panel.collapsed {
  width: var(--panel-width-collapsed);
  transform: translateX(-100%);
}

#chat-panel {
  width: var(--chat-width-expanded);
  transition: width 0.3s ease, transform 0.3s ease;
}

#chat-panel.collapsed {
  width: var(--chat-width-collapsed);
  transform: translateX(100%);
}

/* ===== MOBILE OPTIMIZATIONS ===== */
@media (max-width: 768px) {
  :root {
    --floating-controls-size: 44px;
    --input-panel-height-collapsed: 70px;
    --input-panel-height-expanded: 280px;
  }

  body {
    padding-bottom: var(--input-panel-height-collapsed);
  }

  #input-panel {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    top: auto !important;
    transform: none !important;
    z-index: 50;
    max-height: 85vh;
    width: 100% !important;
    max-width: none !important;
    padding: 0 !important;
    transition: height 0.3s ease, max-height 0.3s ease;
  }

  #input-panel > div {
    border-radius: 16px 16px 0 0 !important;
    border-bottom: none;
  }

  #input-panel.collapsed {
    height: var(--input-panel-height-collapsed);
    max-height: var(--input-panel-height-collapsed);
  }

  #input-panel.collapsed #input-panel-content {
    display: none;
  }

  #input-panel:not(.collapsed) {
    height: auto;
    min-height: var(--input-panel-height-expanded);
  }

  /* Mobile panel header */
  #input-panel .flex.items-center.justify-between {
    padding: 1rem;
    background: var(--surface-1);
    border-bottom: 1px solid var(--border-color);
  }

  /* Mobile textarea */
  #input-panel textarea {
    height: 100px !important;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* Mobile form controls */
  #input-panel select,
  #input-panel button {
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 44px;
  }

  /* Mobile floating action buttons */
  .mobile-fab {
    position: fixed;
    top: 1rem;
    z-index: 45;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    box-shadow: var(--shadow-xl);
  }

  .mobile-fab.left {
    left: 1rem;
  }

  .mobile-fab.right {
    right: 1rem;
  }

  /* Mobile panels */
  #history-panel,
  #chat-panel {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 45;
    width: 320px !important;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    box-shadow: var(--shadow-xl);
  }

  #history-panel.open {
    transform: translateX(0);
  }

  #chat-panel {
    right: 0;
    transform: translateX(100%);
  }

  #chat-panel.open {
    transform: translateX(0);
  }

  /* Mobile backdrop */
  .mobile-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .mobile-backdrop.active {
    opacity: 1;
    visibility: visible;
  }

  /* Hide desktop controls on mobile */
  .hidden-mobile {
    display: none !important;
  }
}

/* ===== FORM ELEMENTS ===== */
input,
textarea,
select,
button {
  font-family: inherit;
  font-size: inherit;
}

textarea {
  resize: vertical;
  min-height: 120px;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.2s ease;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ===== UTILITY CLASSES ===== */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.backdrop-blur {
  backdrop-filter: blur(10px);
}

.glass-effect {
  background: rgba(17, 17, 17, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== RESPONSIVE BREAKPOINTS ===== */
@media (min-width: 640px) {
  .sm\:block {
    display: block;
  }
  .sm\:hidden {
    display: none;
  }
}

@media (min-width: 768px) {
  .md\:block {
    display: block;
  }
  .md\:hidden {
    display: none;
  }
  .md\:flex {
    display: flex;
  }
}

@media (min-width: 1024px) {
  .lg\:block {
    display: block;
  }
  .lg\:hidden {
    display: none;
  }
}

@media (min-width: 1280px) {
  .xl\:block {
    display: block;
  }
  .xl\:hidden {
    display: none;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

svg {
  shape-rendering: optimizeSpeed;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}
.chat-message {
  max-width: 85%;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
}
.user-message {
  background-color: var(--accent-color);
  color: var(--accent-text);
  align-self: flex-end;
  border-bottom-right-radius: 0.25rem;
}
.assistant-message {
  background-color: var(--surface-2);
  align-self: flex-start;
  border-bottom-left-radius: 0.25rem;
}
.assistant-message p,
.assistant-message ul,
.assistant-message ol {
  margin-bottom: 0.5rem;
}
.assistant-message ul,
.assistant-message ol {
  padding-left: 1.5rem;
}
.assistant-message li {
  margin-bottom: 0.25rem;
}
.assistant-message strong {
  color: #fff;
}

/* Collapsible Panel Styles */
.sidebar-collapsed {
  width: 0 !important;
  min-width: 0 !important;
  max-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.chat-collapsed {
  width: 0 !important;
  min-width: 0 !important;
  max-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

/* Panel transitions */
#history-panel {
  transition: all 0.3s ease-in-out;
}

#chat-panel {
  transition: all 0.3s ease-in-out;
}

code {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.1em 0.3em;
  border-radius: 4px;
  font-family: "Courier New", Courier, monospace;
  font-size: 0.9em;
}
pre code {
  background-color: #111 !important;
  display: block;
  padding: 1rem;
  border-radius: 0.5rem;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.modal-content code {
  background-color: #111;
  display: block;
  padding: 1rem;
  border-radius: 0.5rem;
  white-space: pre-wrap;
  word-wrap: break-word;
}
#output svg {
  width: 100%;
  height: auto;
  max-height: 100%;
}
